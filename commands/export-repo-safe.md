---
allowed-tools: Bash, Read, Write
description: Safely export repository to JSON without sensitive data (.env files excluded)
---

# Safe Repository Export

Safely export the repository to JSON format with security-first approach, excluding all .env files and sensitive data.

## Context
- Current directory: !`pwd`
- Repository status: !`git status --porcelain 2>/dev/null | wc -l | xargs echo "files changed:" || echo "Not a git repository"`
- Project info: !`cat package.json 2>/dev/null | grep -E '"name"|"version"' || echo "No package.json found"`

## Your Task

1. **Execute safe repository export**:
   ```bash
   node scripts/repo-to-json.js --no-env --output repo-safe-export.json
   ```

2. **Provide export summary**:
   - Show statistics of exported files
   - Display output file size
   - Confirm no sensitive data included

3. **Generate usage recommendations**:
   - Safe sharing practices
   - Backup strategies
   - Import/restore guidance

## Security Features

✅ **No .env files**: All environment files excluded
✅ **No secrets**: Sensitive data automatically filtered
✅ **Safe defaults**: Uses security-first configuration
✅ **Audit trail**: Provides detailed export statistics

## What Gets Exported

- All source code files
- Configuration files (except .env)
- Documentation
- Project structure
- Package definitions
- Build configurations

## What Gets Excluded

- `.env` files and variants
- `node_modules/`
- `.git/` directory
- Build artifacts
- Log files
- Binary files (unless requested)

Execute the safe export and provide a detailed summary of the exported content.