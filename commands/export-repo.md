---
allowed-tools: Bash, Read, Write
description: Export entire repository to JSON including files, directories, and optionally .env variables
---

# Export Repository to JSON

Export the entire repository structure and contents to JSON format, including files, directories, and optionally environment variables.

## Context
- Current directory: !`pwd`
- Repository info: !`git remote -v 2>/dev/null || echo "Not a git repository"`
- Repository size: !`du -sh . 2>/dev/null | cut -f1`

## Command Options

The export command supports several options:

- `--no-env`: Exclude .env files (RECOMMENDED for security)
- `--include-binary`: Include binary files as base64
- `--output <file>`: Save to specific file
- `--max-size <mb>`: Set maximum file size limit

## Your Task

1. **Execute the repository export command**:
   ```bash
   node scripts/repo-to-json.js $ARGUMENTS
   ```

2. **If no arguments provided, use safe defaults**:
   - Exclude .env files for security
   - Provide export summary and instructions

3. **Security Warning**: Always warn the user if .env files would be included without --no-env flag

4. **Provide helpful guidance**:
   - Explain what was exported
   - Show file size and statistics
   - Suggest safe usage patterns

## Safety Guidelines

⚠️ **CRITICAL**: Always use `--no-env` flag unless explicitly requested otherwise
🔒 **SECURITY**: Warn about sensitive data in .env files
📁 **SIZE**: Monitor output file size for large repositories
🧹 **CLEANUP**: Suggest cleaning temporary export files if needed

## Example Usage Patterns

- **Safe export**: `--no-env --output repo-backup.json`
- **Complete export**: `--include-binary --max-size 100`
- **Quick export**: `--no-env` (outputs to stdout)

Execute the export and provide a comprehensive summary of what was captured.