# Claude Code Hooks Setup

This directory contains custom hooks for Claude Code that provide:

1. **Command blocking** - Prevents dangerous commands like `rm -rf`
2. **Comprehensive logging** - Tracks all tool usage and chat transcripts
3. **Voice notifications** - Text-to-speech alerts when tasks complete
4. **Observability** - Full visibility into agent actions

## Directory Structure

```
~/.claude/hooks/
├── pre_tool_use.py      # Blocks dangerous commands
├── post_tool_use.py     # Logs tool executions
├── stop.py              # Handles completion notifications
├── notification.py      # System notifications
├── sub_agent_stop.py    # Subagent completion tracking
├── utils/
│   ├── text_to_speech.py    # Basic TTS using system commands
│   └── elevenlabs_tts.py    # ElevenLabs API integration
└── logs/                # All hook logs stored here
```

## Setup Instructions

1. **Install uv (Astral's Python package manager)**:
   ```bash
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

2. **Configure ElevenLabs (optional)**:
   - Copy `.env.example` to `.env`: `cp ~/.claude/.env.example ~/.claude/.env`
   - Add your ElevenLabs API key from https://elevenlabs.io/
   - Choose a voice ID from https://api.elevenlabs.io/v1/voices

3. **Restart Claude Code** to activate the hooks

## Features

### Pre-Tool Use Hook
- Blocks `rm -rf` and other dangerous remove commands
- Prevents access to `.env` and secrets files
- Logs all blocked attempts

### Post-Tool Use Hook
- Logs every tool execution with inputs and outputs
- Creates daily summary of tool usage
- Useful for debugging and understanding agent behavior

### Stop Hook
- Saves complete chat transcript after each interaction
- Sends desktop notification (macOS)
- Speaks completion message using TTS

### Logs

All logs are saved in `~/.claude/hooks/logs/` with timestamps:
- `pre_tool_use_*.json` - Blocked commands
- `post_tool_use_*.json` - Tool execution logs
- `chat_*.json` - Complete chat transcripts
- `tools_summary_*.json` - Daily tool usage summary

## Customization

### Adding New Blocked Patterns

Edit `pre_tool_use.py` and add patterns to the `dangerous_patterns` list.

### Changing Completion Messages

Edit the `messages` list in `stop.py` to customize completion announcements.

### Disabling Features

Comment out specific hooks in `~/.claude/settings.json` to disable them.

## Troubleshooting

- If hooks aren't running, ensure scripts are executable: `chmod +x ~/.claude/hooks/*.py`
- Check logs in `~/.claude/hooks/logs/` for debugging information
- For TTS issues, ensure you have `afplay` (macOS) or `mpg123` (Linux) installed
- If ElevenLabs fails, it will fallback to system TTS automatically