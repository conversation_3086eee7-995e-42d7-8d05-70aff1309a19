#!/usr/bin/env -S uv run
# /// script
# requires-python = ">=3.11"
# dependencies = []
# ///

import json
import sys
from datetime import datetime
from pathlib import Path
import subprocess

# Log directory
LOG_DIR = Path.home() / ".claude" / "hooks" / "logs"
LOG_DIR.mkdir(exist_ok=True)

def send_notification(title: str, message: str):
    """Send a system notification"""
    try:
        # macOS notification with sound
        subprocess.run([
            'osascript', '-e',
            f'display notification "{message}" with title "{title}" sound name "Hero"'
        ], capture_output=True)
    except:
        # Fallback to terminal bell
        print('\a', end='', flush=True)

def main():
    # Read input from stdin
    input_data = json.loads(sys.stdin.read())
    
    # Extract notification details
    notification_type = input_data.get("type", "unknown")
    message = input_data.get("message", "Claude Code needs your attention")
    
    # Log the notification
    log_file = LOG_DIR / f"notification_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "notification_type": notification_type,
        "message": message,
        "input_data": input_data
    }
    
    with open(log_file, 'w') as f:
        json.dump(log_entry, f, indent=2)
    
    # Send notification
    if notification_type == "permission_required":
        send_notification("Claude Code Permission", "Your agent needs permission to proceed")
    else:
        send_notification("Claude Code", message)
    
    # Output empty object
    print("{}")

if __name__ == "__main__":
    main()