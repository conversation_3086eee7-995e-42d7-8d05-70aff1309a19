#!/usr/bin/env -S uv run
# /// script
# requires-python = ">=3.11"
# dependencies = []
# ///

import json
import sys
from datetime import datetime
from pathlib import Path

# Log directory
LOG_DIR = Path.home() / ".claude" / "hooks" / "logs"
LOG_DIR.mkdir(exist_ok=True)

def main():
    # Read input from stdin
    input_data = json.loads(sys.stdin.read())
    
    # Create log file for this session
    log_file = LOG_DIR / f"post_tool_use_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    # Extract relevant information
    tool_name = input_data.get("toolName", "")
    tool_input = input_data.get("toolInput", {})
    tool_output = input_data.get("toolOutput", {})
    
    # Create log entry
    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "tool_name": tool_name,
        "tool_input": tool_input,
        "tool_output": tool_output,
        "success": True  # You can analyze toolOutput to determine actual success
    }
    
    # Load existing log data if file exists
    log_data = []
    if log_file.exists():
        with open(log_file, 'r') as f:
            log_data = json.load(f)
    
    # Append new entry
    log_data.append(log_entry)
    
    # Write back to file
    with open(log_file, 'w') as f:
        json.dump(log_data, f, indent=2)
    
    # Also create a summary log of all tools used today
    summary_file = LOG_DIR / f"tools_summary_{datetime.now().strftime('%Y%m%d')}.json"
    
    summary_data = {}
    if summary_file.exists():
        with open(summary_file, 'r') as f:
            summary_data = json.load(f)
    
    # Update tool usage count
    if tool_name not in summary_data:
        summary_data[tool_name] = {
            "count": 0,
            "first_used": datetime.now().isoformat(),
            "last_used": datetime.now().isoformat()
        }
    
    summary_data[tool_name]["count"] += 1
    summary_data[tool_name]["last_used"] = datetime.now().isoformat()
    
    with open(summary_file, 'w') as f:
        json.dump(summary_data, f, indent=2)
    
    # Output empty object (no decision needed for post-tool-use)
    print("{}")

if __name__ == "__main__":
    main()