#!/usr/bin/env -S uv run
# /// script
# requires-python = ">=3.11"
# dependencies = []
# ///

import json
import sys
from datetime import datetime
from pathlib import Path

# Log directory for debugging
LOG_DIR = Path.home() / ".claude" / "hooks" / "logs"
LOG_DIR.mkdir(exist_ok=True)

def is_dangerous_remove_command(tool_name: str, tool_input: dict) -> bool:
    """Check if the command contains dangerous rm patterns"""
    if tool_name != "Bash":
        return False
    
    command = tool_input.get("command", "").strip()
    
    # Dangerous patterns to block
    dangerous_patterns = [
        "rm -rf",
        "rm -fr", 
        "rm -r -f",
        "rm -f -r",
        "rm --recursive --force",
        "rm --force --recursive"
    ]
    
    # Check for dangerous patterns
    for pattern in dangerous_patterns:
        if pattern in command:
            return True
    
    # Also check for rm with important directories
    if command.startswith("rm ") and any(dir in command for dir in ["/", "~", "$HOME", "/etc", "/usr", "/var", "/bin", "/sbin"]):
        return True
    
    return False

def is_environment_file_access(tool_name: str, tool_input: dict) -> bool:
    """Check if trying to access .env files"""
    if tool_name in ["Read", "Edit", "Write"]:
        file_path = tool_input.get("file_path", "")
        if ".env" in file_path or "secrets" in file_path.lower():
            return True
    
    if tool_name == "Bash":
        command = tool_input.get("command", "")
        if any(pattern in command for pattern in [".env", "cat .env", "echo $", "printenv"]):
            return True
    
    return False

def main():
    # Read input from stdin
    input_data = json.loads(sys.stdin.read())
    
    # Log the input for debugging
    log_file = LOG_DIR / f"pre_tool_use_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    # Initialize log data
    log_data = []
    
    # Process the input
    tool_name = input_data.get("toolName", "")
    tool_input = input_data.get("toolInput", {})
    
    # Log entry
    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "tool_name": tool_name,
        "tool_input": tool_input,
        "blocked": False,
        "reason": None
    }
    
    # Check for dangerous commands
    if is_dangerous_remove_command(tool_name, tool_input):
        log_entry["blocked"] = True
        log_entry["reason"] = "Dangerous remove command blocked"
        output = {
            "decision": "block",
            "message": "⚠️  Command blocked: Dangerous rm command detected. Use with caution!"
        }
    elif is_environment_file_access(tool_name, tool_input):
        log_entry["blocked"] = True
        log_entry["reason"] = "Environment file access blocked"
        output = {
            "decision": "block", 
            "message": "🔒 Access blocked: Cannot access environment or secrets files"
        }
    else:
        output = {
            "decision": "allow"
        }
    
    # Append to log file
    if log_file.exists():
        with open(log_file, 'r') as f:
            log_data = json.load(f)
    
    log_data.append(log_entry)
    
    with open(log_file, 'w') as f:
        json.dump(log_data, f, indent=2)
    
    # Output the decision
    print(json.dumps(output))

if __name__ == "__main__":
    main()