#!/usr/bin/env -S uv run
# /// script
# requires-python = ">=3.11"
# dependencies = []
# ///

import json
import sys
from datetime import datetime
from pathlib import Path
import subprocess

# Log directory
LOG_DIR = Path.home() / ".claude" / "hooks" / "logs"
LOG_DIR.mkdir(exist_ok=True)

def save_chat_transcript(input_data: dict):
    """Save the full chat transcript"""
    transcript_path = input_data.get("transcriptPath", "")
    
    if transcript_path and Path(transcript_path).exists():
        # Copy transcript to our logs directory
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        chat_log = LOG_DIR / f"chat_{timestamp}.json"
        
        with open(transcript_path, 'r') as f:
            transcript_data = json.load(f)
        
        with open(chat_log, 'w') as f:
            json.dump(transcript_data, f, indent=2)
        
        return chat_log
    
    return None

def create_completion_message():
    """Create a completion message"""
    messages = [
        "Task completed successfully!",
        "All set and ready for your next request.",
        "Operation finished. What's next?",
        "Done! Ready for the next task.",
        "Completed. Standing by for further instructions."
    ]
    
    # Simple rotation based on current second
    index = datetime.now().second % len(messages)
    return messages[index]

def notify_completion(message: str):
    """Send a notification and speak the message"""
    try:
        # Try macOS notification
        subprocess.run([
            'osascript', '-e', 
            f'display notification "{message}" with title "Claude Code" sound name "Glass"'
        ], capture_output=True)
        
        # Also speak the message using ElevenLabs
        elevenlabs_script = Path.home() / ".claude" / "hooks" / "utils" / "elevenlabs_tts.py"
        if elevenlabs_script.exists():
            subprocess.run([str(elevenlabs_script), message], capture_output=True)
        else:
            # Fallback to basic TTS
            tts_script = Path.home() / ".claude" / "hooks" / "utils" / "text_to_speech.py"
            if tts_script.exists():
                subprocess.run([str(tts_script), message], capture_output=True)
    except:
        # Fallback to terminal bell
        print('\a', end='', flush=True)

def main():
    # Read input from stdin
    input_data = json.loads(sys.stdin.read())
    
    # Create stop log
    log_file = LOG_DIR / f"stop_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    # Save chat transcript
    chat_log_path = save_chat_transcript(input_data)
    
    # Create log entry
    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "transcript_saved": str(chat_log_path) if chat_log_path else None,
        "input_data": input_data
    }
    
    # Save log
    with open(log_file, 'w') as f:
        json.dump(log_entry, f, indent=2)
    
    # Create and send completion notification
    message = create_completion_message()
    notify_completion(message)
    
    # Output empty object
    print("{}")

if __name__ == "__main__":
    main()