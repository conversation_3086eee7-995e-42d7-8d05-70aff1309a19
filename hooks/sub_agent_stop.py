#!/usr/bin/env -S uv run
# /// script
# requires-python = ">=3.11"
# dependencies = []
# ///

import json
import sys
from datetime import datetime
from pathlib import Path
import subprocess

# Log directory
LOG_DIR = Path.home() / ".claude" / "hooks" / "logs"
LOG_DIR.mkdir(exist_ok=True)

def notify_subagent_complete(task_desc: str):
    """Notify when a subagent completes"""
    try:
        # Short notification sound for subagent completion
        subprocess.run([
            'osascript', '-e',
            f'display notification "Subagent completed: {task_desc[:50]}..." with title "Claude Code" sound name "Ping"'
        ], capture_output=True)
    except:
        pass  # Silent fail for subagent notifications

def main():
    # Read input from stdin
    input_data = json.loads(sys.stdin.read())
    
    # Extract subagent information
    task_description = input_data.get("taskDescription", "Unknown task")
    result = input_data.get("result", {})
    
    # Log the subagent completion
    log_file = LOG_DIR / f"sub_agent_stop_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "task_description": task_description,
        "result_summary": str(result)[:500] if result else None,  # Truncate large results
        "input_data": input_data
    }
    
    with open(log_file, 'w') as f:
        json.dump(log_entry, f, indent=2)
    
    # Send notification
    notify_subagent_complete(task_description)
    
    # Output empty object
    print("{}")

if __name__ == "__main__":
    main()