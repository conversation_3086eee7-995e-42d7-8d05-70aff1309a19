#!/bin/bash

echo "Testing Claude Code Hooks..."
echo

# Test pre-tool-use hook with a dangerous command
echo "1. Testing dangerous command blocking:"
echo '{"toolName": "Bash", "toolInput": {"command": "rm -rf /tmp/test"}}' | ~/.claude/hooks/pre_tool_use.py
echo

# Test post-tool-use logging
echo "2. Testing post-tool-use logging:"
echo '{"toolName": "Read", "toolInput": {"file_path": "test.txt"}, "toolOutput": {"content": "test content"}}' | ~/.claude/hooks/post_tool_use.py
echo "Check logs at: ~/.claude/hooks/logs/"
echo

# Test completion notification
echo "3. Testing completion notification (you should hear a voice):"
echo '{"transcriptPath": "/dev/null"}' | ~/.claude/hooks/stop.py
echo

echo "✅ Hook tests complete!"
echo "Logs are saved in: ~/.claude/hooks/logs/"