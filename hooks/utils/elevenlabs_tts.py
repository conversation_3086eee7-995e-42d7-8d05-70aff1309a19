#!/usr/bin/env -S uv run
# /// script
# requires-python = ">=3.11"
# dependencies = [
#     "requests",
#     "python-dotenv"
# ]
# ///

import os
import sys
import json
import requests
from pathlib import Path
import subprocess
import tempfile
from dotenv import load_dotenv

# Load environment variables
load_dotenv(Path.home() / ".claude" / ".env")

# ElevenLabs configuration
ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY")
ELEVENLABS_VOICE_ID = os.getenv("ELEVENLABS_VOICE_ID", "21m00Tcm4TlvDq8ikWAM")  # Rachel voice as default

def speak_with_elevenlabs(text: str, voice_id: str = None):
    """Use ElevenLabs API for text-to-speech"""
    if not ELEVENLABS_API_KEY:
        print("Error: ELEVENLABS_API_KEY not found in ~/.claude/.env", file=sys.stderr)
        return False
    
    voice_id = voice_id or ELEVENLABS_VOICE_ID
    
    url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
    
    headers = {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": ELEVENLABS_API_KEY
    }
    
    data = {
        "text": text,
        "model_id": "eleven_monolingual_v1",
        "voice_settings": {
            "stability": 0.5,
            "similarity_boost": 0.5
        }
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        response.raise_for_status()
        
        # Save audio to temporary file and play it
        with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp_file:
            tmp_file.write(response.content)
            tmp_path = tmp_file.name
        
        # Play the audio file
        if sys.platform == "darwin":  # macOS
            subprocess.run(["afplay", tmp_path], capture_output=True)
        elif sys.platform.startswith("linux"):
            # Try different players
            for player in ["mpg123", "play", "cvlc --play-and-exit"]:
                try:
                    subprocess.run(player.split() + [tmp_path], capture_output=True)
                    break
                except:
                    continue
        
        # Clean up
        os.unlink(tmp_path)
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"ElevenLabs API Error: {e}", file=sys.stderr)
        return False
    except Exception as e:
        print(f"Error playing audio: {e}", file=sys.stderr)
        return False

def main():
    if len(sys.argv) > 1:
        text = " ".join(sys.argv[1:])
    else:
        text = sys.stdin.read().strip()
    
    if text:
        if not speak_with_elevenlabs(text):
            # Fallback to system TTS
            print("Falling back to system TTS", file=sys.stderr)
            if sys.platform == "darwin":
                subprocess.run(["say", text], capture_output=True)

if __name__ == "__main__":
    main()