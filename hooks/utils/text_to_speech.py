#!/usr/bin/env -S uv run
# /// script
# requires-python = ">=3.11"
# dependencies = []
# ///

import sys
import subprocess
import platform

def speak_text(text: str, voice: str = None):
    """
    Simple text-to-speech using system commands
    """
    system = platform.system()
    
    if system == "Darwin":  # macOS
        cmd = ["say"]
        if voice:
            cmd.extend(["-v", voice])
        cmd.append(text)
        
        try:
            subprocess.run(cmd, capture_output=True)
        except Exception as e:
            print(f"TTS Error: {e}", file=sys.stderr)
    
    elif system == "Linux":
        # Try espeak if available
        try:
            subprocess.run(["espeak", text], capture_output=True)
        except:
            # Try festival as fallback
            try:
                subprocess.run(["echo", text, "|", "festival", "--tts"], 
                             shell=True, capture_output=True)
            except:
                print("No TTS engine found on Linux", file=sys.stderr)
    
    else:
        print(f"TTS not supported on {system}", file=sys.stderr)

def main():
    if len(sys.argv) > 1:
        text = " ".join(sys.argv[1:])
        speak_text(text)
    else:
        # Read from stdin
        text = sys.stdin.read().strip()
        if text:
            speak_text(text)

if __name__ == "__main__":
    main()