{"permissions": {"allow": ["<PERSON><PERSON>(claude mcp:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(brew list:*)", "Bash(npm install:*)", "Bash(npx:*)", "Bash(/Users/<USER>/mcp/scripts/test-mcp-server.sh:*)", "<PERSON><PERSON>(chmod:*)", "Bash(/tmp/test-all-mcp-servers.sh:*)", "mcp__mcp-supermemory-ai__search", "mcp__ElevenLabs__check_subscription", "mcp__sequential-thinking__sequentialthinking", "mcp__pica__list_user_connections_and_available_connectors", "mcp__linear-mcp-server__list_teams", "WebFetch(domain:github.com)", "WebFetch(domain:mcp.supermemory.ai)", "WebFetch(domain:raw.githubusercontent.com)", "WebFetch(domain:docs.supermemory.ai)", "mcp__mcp-supermemory-ai__addToSupermemory", "<PERSON><PERSON>(git clone:*)", "Bash(npm run build:*)", "Bash(rm:*)", "WebFetch(domain:aurachat.io)", "Bash(node:*)", "<PERSON><PERSON>(curl:*)", "Bash(./add-test-memories.sh:*)", "Bash(bun test:*)", "Bash(bun run:*)", "Bash(bun run tsc:*)", "<PERSON><PERSON>(cat:*)", "Bash(grep:*)", "Bash(npm search:*)", "Bash(npm view:*)", "Bash(pip install:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__xcode-mcp-server__set_projects_base_dir", "Bash(brew search:*)", "Bash(uv pip install:*)", "<PERSON><PERSON>(uv run:*)", "Bash(/Users/<USER>/mcp-browser-use/.venv/bin/pip install mcp-browser-use)", "Bash(/Users/<USER>/mcp-browser-use/.venv/bin/python -m playwright install)", "Bash(launchctl load:*)", "Bash(echo \"Basic SSH connection command:\nssh root@<proxmox-ip>\n\nOr if using a different port:\nssh -p <port> root@<proxmox-ip>\n\nTo add to SSH config for easier access:\nHost proxmox\n    HostName <proxmox-ip>\n    User root\n    Port 22\")", "Bash(ssh:*)", "Bash(ping:*)", "<PERSON><PERSON>(scp:*)", "mcp__proxmox-production__list_vms", "mcp__proxmox-production__node_status", "mcp__proxmox-production__execute_command", "mcp__ElevenLabs__play_audio", "<PERSON><PERSON>(neo4j status:*)", "<PERSON><PERSON>(neo4j:*)", "<PERSON><PERSON>(python3:*)", "Bash(sudo bash:*)", "Bash(/dev/null)", "mcp__ElevenLabs__text_to_speech", "mcp__proxmox-server__proxmox_get_vm_status", "<PERSON><PERSON>(open:*)", "<PERSON><PERSON>(timeout:*)", "Bash(echo $SHELL)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(alias cc)", "Bash(ngrok:*)", "Bash(cloudflared:*)", "Bash(bore:*)", "Bash(lt:*)", "Bash(frp:*)", "mcp__voice-mode__voice_registry", "Bash(xcodebuild:*)", "<PERSON><PERSON>(pip show:*)", "<PERSON><PERSON>(uv pip show:*)", "mcp__voice-mode__kokoro_start", "mcp__voice-mode__kokoro_status", "Bash(~/.cache/uv/archive-v0/j8YTukHQsEyyrGDJdi1MD/bin/python -c \"import torch; print(f''CUDA available: {torch.cuda.is_available()}''); print(f''CUDA device count: {torch.cuda.device_count() if torch.cuda.is_available() else 0}''); print(f''MPS available: {torch.backends.mps.is_available() if hasattr(torch.backends, \"\"mps\"\") else False}'')\")", "mcp__voice-mode__kokoro_stop", "Bash(~/.cache/uv/archive-v0/j8YTukHQsEyyrGDJdi1MD/bin/kokoro-start --help)", "<PERSON><PERSON>(python test:*)", "Bash(osascript:*)", "Bash(bash:*)", "WebFetch(domain:uncloud.run)", "<PERSON><PERSON>(pkill:*)", "WebFetch(domain:developer.apple.com)"], "deny": []}}