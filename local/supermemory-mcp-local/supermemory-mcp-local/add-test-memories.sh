#!/bin/bash

API_KEY="sm_P6Wwo9WjQQyisDCqccMFs3_bFOEOGCvgLfeiplmNcItmEarJvBPaAbNlPmsiqqNFpgrjACXWrrmTxyIsNyUGaXM"
API_URL="https://api.supermemory.ai/v3/memories"

# Array of test memories
memories=(
  "User prefers using pnpm over npm for package management"
  "Favorite code editor is VS Code with vim keybindings enabled"
  "Preferred git workflow: feature branches with descriptive commit messages"
  "Always uses TypeScript for new projects, never plain JavaScript"
  "Likes to use Tailwind CSS for styling React components"
  "Prefers functional components with hooks over class components"
  "Uses bun for faster package installations when available"
  "Follows clean code principles: DRY, SOLID, and meaningful variable names"
  "Preferred testing framework is Vitest for React applications"
  "Always adds ESLint and Prettier to new projects for code consistency"
)

echo "Adding 10 test memories to Supermemory..."
echo "============================================"

for i in "${!memories[@]}"; do
  memory="${memories[$i]}"
  echo -n "Memory $((i+1)): Adding '${memory:0:50}...' - "
  
  response=$(curl -s -X POST "$API_URL" \
    -H "x-api-key: $API_KEY" \
    -H "Content-Type: application/json" \
    -d "{\"content\": \"$memory\"}")
  
  id=$(echo "$response" | jq -r '.id')
  status=$(echo "$response" | jq -r '.status')
  
  if [ "$id" != "null" ]; then
    echo "✓ Success (ID: $id, Status: $status)"
  else
    echo "✗ Failed"
    echo "Response: $response"
  fi
  
  # Small delay to avoid rate limiting
  sleep 0.5
done

echo ""
echo "Finished adding test memories!"