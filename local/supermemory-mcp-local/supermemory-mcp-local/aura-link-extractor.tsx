// extractLinks.tsx

export function extractComponentLinks(html: string): string[] {
  // Create a DOM parser
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, "text/html");

  // Select all anchor tags that likely point to component pages
  // On Aura, these are usually inside a preview or card, so we look for <a> with hrefs containing "/components/"
  const anchors = Array.from(doc.querySelectorAll('a[href*="/components/"]'));

  // Extract unique hrefs
  const hrefs = Array.from(
    new Set(
      anchors
        .map(a => a.getAttribute("href"))
        .filter((href): href is string => !!href)
    )
  );

  // Optionally, make all links absolute
  const base = "https://aurachat.io";
  return hrefs.map(href => href.startsWith("http") ? href : base + href);
}

// Usage Example in React:
// import { extractComponentLinks } from "./extractLinks";
// 
// // Suppose you have the HTML as a string (e.g., from fetch or file)
// const htmlString = /* ... */;
// 
// const links = extractComponentLinks(htmlString);
// console.log(links);

// Browser console version:
// Run this in the browser console on https://aurachat.io/browse/components?order=recent&page=1
// [...document.querySelectorAll('a[href*="/components/"]')]
//   .map(a => a.href)
//   .filter((v, i, arr) => arr.indexOf(v) === i);