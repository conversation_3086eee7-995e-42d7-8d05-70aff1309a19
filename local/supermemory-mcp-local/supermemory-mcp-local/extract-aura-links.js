import https from 'https';
import fs from 'fs';

function fetchPage(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => resolve(data));
    }).on('error', reject);
  });
}

function extractComponentLinks(html) {
  // Regular expression to find all hrefs containing /components/
  const regex = /href="([^"]*\/components\/[^"]*)"/g;
  const matches = [];
  let match;
  
  while ((match = regex.exec(html)) !== null) {
    matches.push(match[1]);
  }
  
  // Remove duplicates
  const uniqueLinks = [...new Set(matches)];
  
  // Make links absolute
  const base = 'https://aurachat.io';
  return uniqueLinks.map(link => 
    link.startsWith('http') ? link : base + link
  );
}

async function main() {
  try {
    console.log('Fetching Aura components page...');
    const html = await fetchPage('https://aurachat.io/browse/components?order=recent&page=1');
    
    console.log('Extracting component links...');
    const links = extractComponentLinks(html);
    
    console.log(`\nFound ${links.length} unique component links:\n`);
    links.forEach(link => console.log(link));
    
    // Save to file
    fs.writeFileSync('aura-component-links.json', JSON.stringify(links, null, 2));
    console.log('\nLinks saved to aura-component-links.json');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

main();