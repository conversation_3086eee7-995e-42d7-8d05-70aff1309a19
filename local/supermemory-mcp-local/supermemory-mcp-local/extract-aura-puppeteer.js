import fetch from 'node-fetch';

async function extractWithFetch() {
  try {
    const response = await fetch('https://aurachat.io/browse/components?order=recent&page=1', {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      }
    });
    
    const html = await response.text();
    console.log('HTML length:', html.length);
    console.log('First 1000 chars:', html.substring(0, 1000));
    
    // Look for any component links
    const linkRegex = /\/components\/[a-zA-Z0-9-]+/g;
    const matches = html.match(linkRegex) || [];
    const uniqueLinks = [...new Set(matches)];
    
    console.log('\nFound links:', uniqueLinks);
    
    // Also look for JSON data
    const jsonRegex = /\{[^{}]*"components"[^{}]*\}/g;
    const jsonMatches = html.match(jsonRegex);
    if (jsonMatches) {
      console.log('\nFound JSON data:', jsonMatches[0].substring(0, 200));
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

extractWithFetch();