import fetch from 'node-fetch';

// Common API patterns to try
const apiPatterns = [
  '/api/components',
  '/api/browse/components',
  '/api/v1/components',
  '/components.json',
  '/browse/api/components',
  '/.netlify/functions/components',
  '/api/components/browse'
];

async function tryEndpoint(baseUrl, endpoint, params) {
  const url = `${baseUrl}${endpoint}?${params}`;
  console.log(`Trying: ${url}`);
  
  try {
    const response = await fetch(url, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0'
      }
    });
    
    if (response.ok) {
      const contentType = response.headers.get('content-type');
      console.log(`✓ Success! Status: ${response.status}, Content-Type: ${contentType}`);
      
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json();
        console.log('Response preview:', JSON.stringify(data).substring(0, 200));
        return data;
      }
    } else {
      console.log(`✗ Failed: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.log(`✗ Error: ${error.message}`);
  }
  
  return null;
}

async function findApi() {
  const baseUrl = 'https://aurachat.io';
  const params = 'order=recent&page=1';
  
  for (const endpoint of apiPatterns) {
    const result = await tryEndpoint(baseUrl, endpoint, params);
    if (result) {
      console.log('\n🎉 Found working endpoint!');
      return { endpoint, data: result };
    }
  }
  
  console.log('\n❌ No API endpoints found');
}

findApi();