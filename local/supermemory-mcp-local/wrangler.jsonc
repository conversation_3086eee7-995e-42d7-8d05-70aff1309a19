/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
    "$schema": "node_modules/wrangler/config-schema.json",
    "name": "supermemory-mcp-app",
    "compatibility_date": "2025-04-04",
    "main": "./workers/app.ts",
    "observability": {
        "enabled": true
    },
    "compatibility_flags": [
        "nodejs_compat",
        "nodejs_compat_populate_process_env"
    ],
    "durable_objects": {
        "bindings": [
            {
                "name": "MY_DO",
                "class_name": "MyDurableObject"
            }
        ]
    },
    "migrations": [{ "tag": "v1", "new_sqlite_classes": ["MyDurableObject"] }]

    /**
     * Smart Placement
     * Docs: https://developers.cloudflare.com/workers/configuration/smart-placement/#smart-placement
     */
    // "placement": { "mode": "smart" },

    /**
     * Bindings
     * Bindings allow your Worker to interact with resources on the Cloudflare Developer Platform, including
     * databases, object storage, AI inference, real-time communication and more.
     * https://developers.cloudflare.com/workers/runtime-apis/bindings/
     */

    /**
     * Environment Variables
     * https://developers.cloudflare.com/workers/wrangler/configuration/#environment-variables
     */
    // "vars": { "MY_VARIABLE": "production_value" },
    /**
     * Note: Use secrets to store sensitive data.
     * https://developers.cloudflare.com/workers/configuration/secrets/
     */

    /**
     * Static Assets
     * https://developers.cloudflare.com/workers/static-assets/binding/
     */
    // "assets": { "directory": "./public/", "binding": "ASSETS" },

    /**
     * Service Bindings (communicate between multiple Workers)
     * https://developers.cloudflare.com/workers/wrangler/configuration/#service-bindings
     */
    // "services": [{ "binding": "MY_SERVICE", "service": "my-service" }]
}
