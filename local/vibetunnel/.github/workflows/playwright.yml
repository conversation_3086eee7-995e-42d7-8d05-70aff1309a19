name: Playwright Tests

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
    paths:
      - 'web/**'
      - '.github/workflows/playwright.yml'

permissions:
  pull-requests: write
  issues: write

jobs:
  test:
    name: Playwright E2E Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          
      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.12.1
          
      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV
          
      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-
            
      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libpam0g-dev xvfb
          
      - name: Install dependencies
        working-directory: ./web
        run: pnpm install
        
      - name: Build application
        working-directory: ./web
        run: pnpm run build
        
      - name: Install Playwright browsers
        working-directory: ./web
        run: pnpm exec playwright install --with-deps chromium
        
      - name: Run Playwright tests
        working-directory: ./web
        run: xvfb-run -a pnpm test:e2e
        env:
          CI: true
          
      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: web/playwright-report/
          retention-days: 7
          
      - name: Upload test videos
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-videos
          path: web/test-results/
          retention-days: 7