name: Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., 1.2.3)'
        required: true
        type: string

permissions:
  contents: write
  issues: write
  pull-requests: write

jobs:
  build-mac:
    name: Build macOS App
    runs-on: macos-15
    env:
      GITHUB_REPO_NAME: ${{ github.repository }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Select Xcode 16.3
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: '16.4'
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '24'
    
    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 9
        run_install: false
    
    - name: Get pnpm store directory
      shell: bash
      run: |
        echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV
    
    - name: Setup pnpm cache
      uses: useblacksmith/cache@v5
      with:
        path: ${{ env.STORE_PATH }}
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('web/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-
    
    - name: Setup Bun
      uses: oven-sh/setup-bun@v2
      with:
        bun-version: latest
    
    - name: Install web dependencies
      working-directory: web
      run: pnpm install --frozen-lockfile
    
    - name: Resolve Dependencies
      working-directory: mac
      run: |
        xcodebuild -resolvePackageDependencies -workspace VibeTunnel.xcworkspace
    
    - name: Build Release (arm64)
      working-directory: mac
      run: |
        ./scripts/build.sh --configuration Release --arch arm64
        mv build/Build/Products/Release/VibeTunnel.app build/Build/Products/Release/VibeTunnel-arm64.app
    
    - name: Build Release (x86_64)
      working-directory: mac
      run: |
        # Clean build directory for x86_64 build
        rm -rf build/Build/Products/Release/VibeTunnel.app
        ./scripts/build.sh --configuration Release --arch x86_64
        mv build/Build/Products/Release/VibeTunnel.app build/Build/Products/Release/VibeTunnel-x86_64.app
    
    - name: Create DMGs and ZIPs
      working-directory: mac
      run: |
        VERSION="${{ github.event.inputs.version || github.ref_name }}"
        VERSION="${VERSION#v}"  # Remove 'v' prefix if present
        
        # Create arm64 DMG and ZIP
        ./scripts/create-dmg.sh "build/Build/Products/Release/VibeTunnel-arm64.app"
        ./scripts/create-zip.sh "build/Build/Products/Release/VibeTunnel-arm64.app"
        
        # Create Intel DMG and ZIP
        ./scripts/create-dmg.sh "build/Build/Products/Release/VibeTunnel-x86_64.app"
        ./scripts/create-zip.sh "build/Build/Products/Release/VibeTunnel-x86_64.app"
        
        # List created files
        echo "Created files:"
        ls -la build/*.dmg build/*.zip
    
    - name: Upload Release Artifacts
      uses: actions/upload-artifact@v4
      with:
        name: mac-release
        path: |
          mac/build/*.dmg
          mac/build/*.zip
        retention-days: 7
    
    - name: Create GitHub Release
      if: startsWith(github.ref, 'refs/tags/')
      uses: softprops/action-gh-release@v2
      with:
        files: |
          mac/build/*.dmg
          mac/build/*.zip
        draft: true
        prerelease: ${{ contains(github.ref_name, 'beta') || contains(github.ref_name, 'rc') }}
        generate_release_notes: true

  build-ios:
    name: Build iOS App
    runs-on: macos-15
    env:
      GITHUB_REPO_NAME: ${{ github.repository }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Select Xcode 16.3
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: '16.4'
    
    - name: Resolve Dependencies
      working-directory: ios
      run: |
        xcodebuild -resolvePackageDependencies -project VibeTunnel.xcodeproj
    
    - name: Build iOS Release
      working-directory: ios
      run: |
        xcodebuild build \
          -project VibeTunnel.xcodeproj \
          -scheme VibeTunnel \
          -destination "generic/platform=iOS" \
          -configuration Release \
          CODE_SIGNING_ALLOWED=NO \
          CODE_SIGNING_REQUIRED=NO \
          ONLY_ACTIVE_ARCH=NO \
          -derivedDataPath build/DerivedData
    
    - name: Upload iOS Artifacts
      uses: actions/upload-artifact@v4
      with:
        name: ios-release
        path: ios/build/DerivedData/Build/Products/Release-iphoneos/
        retention-days: 7