name: Web CI

on:
  push:
    branches: [ main, ms-pty ]
    paths:
      - 'web/**'
      - '.github/workflows/web-ci.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'web/**'
      - '.github/workflows/web-ci.yml'

permissions:
  pull-requests: write
  issues: write

defaults:
  run:
    working-directory: web

jobs:
  lint-and-type-check:
    name: <PERSON>t and Type Check
    runs-on: blacksmith-8vcpu-ubuntu-2404-arm
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.12.1

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'
          cache-dependency-path: 'web/pnpm-lock.yaml'

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libpam0g-dev

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run linting
        run: pnpm run lint

      - name: Run type checking
        run: pnpm run typecheck

      - name: Check formatting
        run: pnpm run format:check

  build:
    name: Build
    runs-on: blacksmith-8vcpu-ubuntu-2404-arm
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.12.1

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'
          cache-dependency-path: 'web/pnpm-lock.yaml'

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libpam0g-dev

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build project
        run: pnpm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: web-build
          path: |
            web/dist/
            web/public/
          retention-days: 7

  test:
    name: Test
    runs-on: blacksmith-8vcpu-ubuntu-2404-arm
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.12.1

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'
          cache-dependency-path: 'web/pnpm-lock.yaml'

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libpam0g-dev

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run tests
        run: pnpm run test:ci

      - name: Upload coverage
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: coverage-report
          path: web/coverage/
          retention-days: 7