// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		0DE52F23A39D42802C0A6FA5 /* ServerConfigTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 26508D4774F932BE603A3A92 /* ServerConfigTests.swift */; };
		19D4521F6D3D74D22F5300BF /* EdgeCaseTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210B160CA1C5185A85EE359C /* EdgeCaseTests.swift */; };
		2B54F778A3BC2CBBBB80E5D7 /* BufferWebSocketClientTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = C90343824B8E77C08ECFC3B1 /* BufferWebSocketClientTests.swift */; };
		30930920937DDB40554E42F3 /* ConnectionManagerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = EFA3E0BED8017D7540A6B822 /* ConnectionManagerTests.swift */; };
		365CCFA1CC50361FFC9B354A /* MockURLProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = CCEB1F27FFC1C75B53ED3EE5 /* MockURLProtocol.swift */; };
		3B287C1FA60A638751E34631 /* PerformanceTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D852A5C2BBD22D92B578BFE /* PerformanceTests.swift */; };
		42139B23F5E381A7E9CB20C6 /* APIClientTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5C009651579E8C0E4ADD20DC /* APIClientTests.swift */; };
		51D8512C630C0A818A2651F4 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7C27ECCFC47CD72452A9FD3E /* Foundation.framework */; };
		52F27E28E407419D23E6178B /* TestTags.swift in Sources */ = {isa = PBXBuildFile; fileRef = D020D89B8296E14F390D9C03 /* TestTags.swift */; };
		7272E2FE084BB86440A69701 /* SwiftTerm in Frameworks */ = {isa = PBXBuildFile; productRef = 51D59A51D50DE7492ECEAA6F /* SwiftTerm */; };
		8C09457B954A8495B78173D1 /* MockAPIClient.swift in Sources */ = {isa = PBXBuildFile; fileRef = DA2D6C604D5F6878F2F6337E /* MockAPIClient.swift */; };
		8E34DF84C86F98478D12452D /* APIErrorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31BCE90BB6113D30ABBD29B2 /* APIErrorTests.swift */; };
		99BDF6EDB39E853DBABFC6BF /* MockWebSocketTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 778FE49DC79E8514CD11A94F /* MockWebSocketTask.swift */; };
		A16695E8AAEDEA40299D6471 /* TestFixtures.swift in Sources */ = {isa = PBXBuildFile; fileRef = B38BEE7FE833B6FDC3A56BCE /* TestFixtures.swift */; };
		A937560409D02D2B5F4CF5C1 /* StandaloneTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFA051A5F108D190542BC035 /* StandaloneTests.swift */; };
		B204F9E31D856D1C7EF682AC /* TerminalParsingTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = A118F0B3208513EEF0373B69 /* TerminalParsingTests.swift */; };
		C6460D6D0DC21C3A4D946D35 /* VibeTunnelTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 64175D4F50D00AA004E41143 /* VibeTunnelTests.swift */; };
		CCAECFDA5A092DC60C117DD2 /* SessionTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = D6282B1FFEB2C1844BD4BC05 /* SessionTests.swift */; };
		D939ED550D6C0D978BD8C4B4 /* AuthenticationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = B7FB8482C0055D3A8D80EE21 /* AuthenticationTests.swift */; };
		E08E940B186A0335737B81CF /* WebSocketReconnectionTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = AB3C38909F88E2D50282BC51 /* WebSocketReconnectionTests.swift */; };
		EDBCD977F9C7836FBF3E46DC /* FileSystemTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 36DEB689EB0358B8710343B9 /* FileSystemTests.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		FE415EBDC086853977484B28 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4C82CEFDA8E4EFE0B37538D7 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 788687F02DFF4FCB00B22C15;
			remoteInfo = VibeTunnel;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		1D852A5C2BBD22D92B578BFE /* PerformanceTests.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = PerformanceTests.swift; sourceTree = "<group>"; };
		210B160CA1C5185A85EE359C /* EdgeCaseTests.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = EdgeCaseTests.swift; sourceTree = "<group>"; };
		216D52E392D8F0CDC6358BB8 /* VibeTunnel.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = VibeTunnel.app; sourceTree = BUILT_PRODUCTS_DIR; };
		26508D4774F932BE603A3A92 /* ServerConfigTests.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ServerConfigTests.swift; path = Models/ServerConfigTests.swift; sourceTree = "<group>"; };
		31BCE90BB6113D30ABBD29B2 /* APIErrorTests.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = APIErrorTests.swift; sourceTree = "<group>"; };
		36DEB689EB0358B8710343B9 /* FileSystemTests.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = FileSystemTests.swift; sourceTree = "<group>"; };
		5C009651579E8C0E4ADD20DC /* APIClientTests.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = APIClientTests.swift; path = Services/APIClientTests.swift; sourceTree = "<group>"; };
		64175D4F50D00AA004E41143 /* VibeTunnelTests.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = VibeTunnelTests.swift; sourceTree = "<group>"; };
		651BDD47248F3D9D55994B3E /* VibeTunnelTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = VibeTunnelTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		778FE49DC79E8514CD11A94F /* MockWebSocketTask.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MockWebSocketTask.swift; path = Mocks/MockWebSocketTask.swift; sourceTree = "<group>"; };
		7C27ECCFC47CD72452A9FD3E /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		A118F0B3208513EEF0373B69 /* TerminalParsingTests.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = TerminalParsingTests.swift; sourceTree = "<group>"; };
		AB3C38909F88E2D50282BC51 /* WebSocketReconnectionTests.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = WebSocketReconnectionTests.swift; sourceTree = "<group>"; };
		B38BEE7FE833B6FDC3A56BCE /* TestFixtures.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TestFixtures.swift; path = Utilities/TestFixtures.swift; sourceTree = "<group>"; };
		B7FB8482C0055D3A8D80EE21 /* AuthenticationTests.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AuthenticationTests.swift; sourceTree = "<group>"; };
		C90343824B8E77C08ECFC3B1 /* BufferWebSocketClientTests.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BufferWebSocketClientTests.swift; path = Services/BufferWebSocketClientTests.swift; sourceTree = "<group>"; };
		CCEB1F27FFC1C75B53ED3EE5 /* MockURLProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MockURLProtocol.swift; path = Mocks/MockURLProtocol.swift; sourceTree = "<group>"; };
		D020D89B8296E14F390D9C03 /* TestTags.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TestTags.swift; path = Utilities/TestTags.swift; sourceTree = "<group>"; };
		D6282B1FFEB2C1844BD4BC05 /* SessionTests.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SessionTests.swift; path = Models/SessionTests.swift; sourceTree = "<group>"; };
		DA2D6C604D5F6878F2F6337E /* MockAPIClient.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MockAPIClient.swift; path = Mocks/MockAPIClient.swift; sourceTree = "<group>"; };
		DFA051A5F108D190542BC035 /* StandaloneTests.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = StandaloneTests.swift; sourceTree = "<group>"; };
		EFA3E0BED8017D7540A6B822 /* ConnectionManagerTests.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConnectionManagerTests.swift; path = Services/ConnectionManagerTests.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		78868B612DFF808300B22C15 /* Exceptions for "VibeTunnel" folder in "VibeTunnel" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Local.xcconfig,
				Resources/Info.plist,
				Shared.xcconfig,
				version.xcconfig,
			);
			target = 788687F02DFF4FCB00B22C15 /* VibeTunnel */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		A4640175A87A16583F0A76A8 /* VibeTunnel */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				78868B612DFF808300B22C15 /* Exceptions for "VibeTunnel" folder in "VibeTunnel" target */,
			);
			path = VibeTunnel;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		050F0F5073334BBC53FFBEE5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				51D8512C630C0A818A2651F4 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2FD464BF4BA1F22E2EBF0847 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7272E2FE084BB86440A69701 /* SwiftTerm in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0DD83B4196D66608380E46BB = {
			isa = PBXGroup;
			children = (
				A4640175A87A16583F0A76A8 /* VibeTunnel */,
				7866022B46DD9C411CAEDA26 /* Products */,
				BA0784F47C0CF8EA73E68265 /* Frameworks */,
				762BBB767A3EED9FAC65416B /* VibeTunnelTests */,
			);
			sourceTree = "<group>";
		};
		762BBB767A3EED9FAC65416B /* VibeTunnelTests */ = {
			isa = PBXGroup;
			children = (
				31BCE90BB6113D30ABBD29B2 /* APIErrorTests.swift */,
				B7FB8482C0055D3A8D80EE21 /* AuthenticationTests.swift */,
				210B160CA1C5185A85EE359C /* EdgeCaseTests.swift */,
				36DEB689EB0358B8710343B9 /* FileSystemTests.swift */,
				1D852A5C2BBD22D92B578BFE /* PerformanceTests.swift */,
				DFA051A5F108D190542BC035 /* StandaloneTests.swift */,
				A118F0B3208513EEF0373B69 /* TerminalParsingTests.swift */,
				64175D4F50D00AA004E41143 /* VibeTunnelTests.swift */,
				AB3C38909F88E2D50282BC51 /* WebSocketReconnectionTests.swift */,
				DA2D6C604D5F6878F2F6337E /* MockAPIClient.swift */,
				CCEB1F27FFC1C75B53ED3EE5 /* MockURLProtocol.swift */,
				778FE49DC79E8514CD11A94F /* MockWebSocketTask.swift */,
				26508D4774F932BE603A3A92 /* ServerConfigTests.swift */,
				D6282B1FFEB2C1844BD4BC05 /* SessionTests.swift */,
				5C009651579E8C0E4ADD20DC /* APIClientTests.swift */,
				C90343824B8E77C08ECFC3B1 /* BufferWebSocketClientTests.swift */,
				EFA3E0BED8017D7540A6B822 /* ConnectionManagerTests.swift */,
				B38BEE7FE833B6FDC3A56BCE /* TestFixtures.swift */,
				D020D89B8296E14F390D9C03 /* TestTags.swift */,
			);
			path = VibeTunnelTests;
			sourceTree = SOURCE_ROOT;
		};
		7866022B46DD9C411CAEDA26 /* Products */ = {
			isa = PBXGroup;
			children = (
				216D52E392D8F0CDC6358BB8 /* VibeTunnel.app */,
				651BDD47248F3D9D55994B3E /* VibeTunnelTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BA0784F47C0CF8EA73E68265 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				F32522FE179C41E1B2445A65 /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		F32522FE179C41E1B2445A65 /* iOS */ = {
			isa = PBXGroup;
			children = (
				7C27ECCFC47CD72452A9FD3E /* Foundation.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		04469FB37E8A42F9D06BF670 /* VibeTunnelTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E2F3E41AD9740FC10D5963F6 /* Build configuration list for PBXNativeTarget "VibeTunnelTests" */;
			buildPhases = (
				62833EC4917888F3D96423A2 /* Sources */,
				050F0F5073334BBC53FFBEE5 /* Frameworks */,
				2FCC8DEBCE2261F1453AA9F2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AE0F4BA931358FE4AFA17B94 /* PBXTargetDependency */,
			);
			name = VibeTunnelTests;
			productName = VibeTunnelTests;
			productReference = 651BDD47248F3D9D55994B3E /* VibeTunnelTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		788687F02DFF4FCB00B22C15 /* VibeTunnel */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8D99F6108356CC0D04FBF9FD /* Build configuration list for PBXNativeTarget "VibeTunnel" */;
			buildPhases = (
				6B5EFF9E40E19685A12D29C9 /* Sources */,
				2FD464BF4BA1F22E2EBF0847 /* Frameworks */,
				A1EBEB60F7F8C33C5FF37ACF /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				A4640175A87A16583F0A76A8 /* VibeTunnel */,
			);
			name = VibeTunnel;
			packageProductDependencies = (
				51D59A51D50DE7492ECEAA6F /* SwiftTerm */,
			);
			productName = VibeTunnel;
			productReference = 216D52E392D8F0CDC6358BB8 /* VibeTunnel.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		4C82CEFDA8E4EFE0B37538D7 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 2600;
				TargetAttributes = {
					04469FB37E8A42F9D06BF670 = {
						TestTargetID = 788687F02DFF4FCB00B22C15;
					};
					788687F02DFF4FCB00B22C15 = {
						CreatedOnToolsVersion = 16.0;
					};
				};
			};
			buildConfigurationList = 6D69C4974433AED85B9B8A62 /* Build configuration list for PBXProject "VibeTunnel-iOS" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 0DD83B4196D66608380E46BB;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				99D7F1C619326805A282313E /* XCRemoteSwiftPackageReference "SwiftTerm" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 7866022B46DD9C411CAEDA26 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				788687F02DFF4FCB00B22C15 /* VibeTunnel */,
				04469FB37E8A42F9D06BF670 /* VibeTunnelTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2FCC8DEBCE2261F1453AA9F2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A1EBEB60F7F8C33C5FF37ACF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		62833EC4917888F3D96423A2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8E34DF84C86F98478D12452D /* APIErrorTests.swift in Sources */,
				D939ED550D6C0D978BD8C4B4 /* AuthenticationTests.swift in Sources */,
				19D4521F6D3D74D22F5300BF /* EdgeCaseTests.swift in Sources */,
				EDBCD977F9C7836FBF3E46DC /* FileSystemTests.swift in Sources */,
				3B287C1FA60A638751E34631 /* PerformanceTests.swift in Sources */,
				A937560409D02D2B5F4CF5C1 /* StandaloneTests.swift in Sources */,
				B204F9E31D856D1C7EF682AC /* TerminalParsingTests.swift in Sources */,
				C6460D6D0DC21C3A4D946D35 /* VibeTunnelTests.swift in Sources */,
				E08E940B186A0335737B81CF /* WebSocketReconnectionTests.swift in Sources */,
				8C09457B954A8495B78173D1 /* MockAPIClient.swift in Sources */,
				365CCFA1CC50361FFC9B354A /* MockURLProtocol.swift in Sources */,
				99BDF6EDB39E853DBABFC6BF /* MockWebSocketTask.swift in Sources */,
				0DE52F23A39D42802C0A6FA5 /* ServerConfigTests.swift in Sources */,
				CCAECFDA5A092DC60C117DD2 /* SessionTests.swift in Sources */,
				42139B23F5E381A7E9CB20C6 /* APIClientTests.swift in Sources */,
				2B54F778A3BC2CBBBB80E5D7 /* BufferWebSocketClientTests.swift in Sources */,
				30930920937DDB40554E42F3 /* ConnectionManagerTests.swift in Sources */,
				A16695E8AAEDEA40299D6471 /* TestFixtures.swift in Sources */,
				52F27E28E407419D23E6178B /* TestTags.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6B5EFF9E40E19685A12D29C9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		AE0F4BA931358FE4AFA17B94 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = VibeTunnel;
			target = 788687F02DFF4FCB00B22C15 /* VibeTunnel */;
			targetProxy = FE415EBDC086853977484B28 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		2287F1E073ABA2294F8D65E6 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = A4640175A87A16583F0A76A8 /* VibeTunnel */;
			baseConfigurationReferenceRelativePath = Shared.xcconfig;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = Y5PE65HELJ;
				ENABLE_PREVIEWS = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"DEBUG=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = VibeTunnel/Resources/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.developer-tools";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		6BD919EBC6E8FC8AE5C0AD08 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGN_STYLE = Automatic;
				ENABLE_TESTING_FRAMEWORKS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				PRODUCT_BUNDLE_IDENTIFIER = "sh.vibetunnel.VibeTunnelTests-Mobile";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_VERSION = 6.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/VibeTunnel.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/VibeTunnel";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		6BDD461861CF6891A0D3CAAA /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = A4640175A87A16583F0A76A8 /* VibeTunnel */;
			baseConfigurationReferenceRelativePath = Shared.xcconfig;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = Y5PE65HELJ;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_PREVIEWS = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = VibeTunnel/Resources/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.developer-tools";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		AB5CCE958CDF40666B1D5C7F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGN_STYLE = Automatic;
				ENABLE_TESTING_FRAMEWORKS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				PRODUCT_BUNDLE_IDENTIFIER = "sh.vibetunnel.VibeTunnelTests-Mobile";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_VERSION = 6.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/VibeTunnel.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/VibeTunnel";
			};
			name = Debug;
		};
		E5ADA5251EC344824C852777 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		F5CF3F9AC6801B6BE276B81C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		6D69C4974433AED85B9B8A62 /* Build configuration list for PBXProject "VibeTunnel-iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E5ADA5251EC344824C852777 /* Debug */,
				F5CF3F9AC6801B6BE276B81C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		8D99F6108356CC0D04FBF9FD /* Build configuration list for PBXNativeTarget "VibeTunnel" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2287F1E073ABA2294F8D65E6 /* Debug */,
				6BDD461861CF6891A0D3CAAA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		E2F3E41AD9740FC10D5963F6 /* Build configuration list for PBXNativeTarget "VibeTunnelTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6BD919EBC6E8FC8AE5C0AD08 /* Release */,
				AB5CCE958CDF40666B1D5C7F /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		99D7F1C619326805A282313E /* XCRemoteSwiftPackageReference "SwiftTerm" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/migueldeicaza/SwiftTerm";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.2.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		51D59A51D50DE7492ECEAA6F /* SwiftTerm */ = {
			isa = XCSwiftPackageProductDependency;
			package = 99D7F1C619326805A282313E /* XCRemoteSwiftPackageReference "SwiftTerm" */;
			productName = SwiftTerm;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 4C82CEFDA8E4EFE0B37538D7 /* Project object */;
}
