// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		78AD8B952E051ED40009725C /* Logging in Frameworks */ = {isa = PBXBuildFile; productRef = 78AD8B942E051ED40009725C /* Logging */; };
		89D01D862CB5D7DC0075D8BD /* Sparkle in Frameworks */ = {isa = PBXBuildFile; productRef = 89D01D852CB5D7DC0075D8BD /* Sparkle */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		788687FF2DFF4FCB00B22C15 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 788687E92DFF4FCB00B22C15 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 788687F02DFF4FCB00B22C15;
			remoteInfo = VibeTunnel;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		788687F12DFF4FCB00B22C15 /* VibeTunnel.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = VibeTunnel.app; sourceTree = BUILT_PRODUCTS_DIR; };
		788687FE2DFF4FCB00B22C15 /* VibeTunnelTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = VibeTunnelTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		78868B612DFF808300B22C15 /* Exceptions for "VibeTunnel" folder in "VibeTunnel" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
				Shared.xcconfig,
				version.xcconfig,
			);
			target = 788687F02DFF4FCB00B22C15 /* VibeTunnel */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		788687F32DFF4FCB00B22C15 /* VibeTunnel */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				78868B612DFF808300B22C15 /* Exceptions for "VibeTunnel" folder in "VibeTunnel" target */,
			);
			path = VibeTunnel;
			sourceTree = "<group>";
		};
		788688012DFF4FCB00B22C15 /* VibeTunnelTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = VibeTunnelTests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		788687EE2DFF4FCB00B22C15 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				78AD8B952E051ED40009725C /* Logging in Frameworks */,
				89D01D862CB5D7DC0075D8BD /* Sparkle in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		788687FB2DFF4FCB00B22C15 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		788687E82DFF4FCB00B22C15 = {
			isa = PBXGroup;
			children = (
				788687F32DFF4FCB00B22C15 /* VibeTunnel */,
				788688012DFF4FCB00B22C15 /* VibeTunnelTests */,
				78AD8B8F2E051ED40009725C /* Frameworks */,
				788687F22DFF4FCB00B22C15 /* Products */,
			);
			sourceTree = "<group>";
		};
		788687F22DFF4FCB00B22C15 /* Products */ = {
			isa = PBXGroup;
			children = (
				788687F12DFF4FCB00B22C15 /* VibeTunnel.app */,
				788687FE2DFF4FCB00B22C15 /* VibeTunnelTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		78AD8B8F2E051ED40009725C /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		788687F02DFF4FCB00B22C15 /* VibeTunnel */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 788688152DFF4FCC00B22C15 /* Build configuration list for PBXNativeTarget "VibeTunnel" */;
			buildPhases = (
				C3D4E5F6A7B8C9D0E1F23456 /* Install Build Dependencies */,
				788687ED2DFF4FCB00B22C15 /* Sources */,
				788687EE2DFF4FCB00B22C15 /* Frameworks */,
				788687EF2DFF4FCB00B22C15 /* Resources */,
				A1B2C3D4E5F6789012345678 /* Calculate Web Hash */,
				B2C3D4E5F6A7B8C9D0E1F234 /* Build Web Frontend */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				788687F32DFF4FCB00B22C15 /* VibeTunnel */,
			);
			name = VibeTunnel;
			packageProductDependencies = (
				89D01D852CB5D7DC0075D8BD /* Sparkle */,
				78AD8B942E051ED40009725C /* Logging */,
			);
			productName = VibeTunnel;
			productReference = 788687F12DFF4FCB00B22C15 /* VibeTunnel.app */;
			productType = "com.apple.product-type.application";
		};
		788687FD2DFF4FCB00B22C15 /* VibeTunnelTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 788688182DFF4FCC00B22C15 /* Build configuration list for PBXNativeTarget "VibeTunnelTests" */;
			buildPhases = (
				788687FA2DFF4FCB00B22C15 /* Sources */,
				788687FB2DFF4FCB00B22C15 /* Frameworks */,
				788687FC2DFF4FCB00B22C15 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				788688002DFF4FCB00B22C15 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				788688012DFF4FCB00B22C15 /* VibeTunnelTests */,
			);
			name = VibeTunnelTests;
			productName = VibeTunnelTests;
			productReference = 788687FE2DFF4FCB00B22C15 /* VibeTunnelTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		788687E92DFF4FCB00B22C15 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1610;
				LastUpgradeCheck = 2600;
				TargetAttributes = {
					788687F02DFF4FCB00B22C15 = {
						CreatedOnToolsVersion = 16.1;
					};
					788687FD2DFF4FCB00B22C15 = {
						CreatedOnToolsVersion = 16.1;
						TestTargetID = 788687F02DFF4FCB00B22C15;
					};
				};
			};
			buildConfigurationList = 788687EC2DFF4FCB00B22C15 /* Build configuration list for PBXProject "VibeTunnel-Mac" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 788687E82DFF4FCB00B22C15;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				89D01D842CB5D7DC0075D8BD /* XCRemoteSwiftPackageReference "Sparkle" */,
				78AD8B8E2E051EB50009725C /* XCRemoteSwiftPackageReference "swift-log" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 788687F22DFF4FCB00B22C15 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				788687F02DFF4FCB00B22C15 /* VibeTunnel */,
				788687FD2DFF4FCB00B22C15 /* VibeTunnelTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		788687EF2DFF4FCB00B22C15 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		788687FC2DFF4FCB00B22C15 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		A1B2C3D4E5F6789012345678 /* Calculate Web Hash */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Calculate Web Hash";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(BUILT_PRODUCTS_DIR)/.web-content-hash",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/zsh;
			shellScript = "# Calculate hash of web content\necho \"Calculating web content hash...\"\n\n# Run the hash calculation script\n\"${SRCROOT}/scripts/calculate-web-hash.sh\"\n\nif [ $? -ne 0 ]; then\n    echo \"error: Failed to calculate web hash\"\n    exit 1\nfi\n";
		};
		B2C3D4E5F6A7B8C9D0E1F234 /* Build Web Frontend */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/.web-content-hash",
			);
			name = "Build Web Frontend";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(CONTENTS_FOLDER_PATH)/Resources/web/public",
				"$(BUILT_PRODUCTS_DIR)/$(CONTENTS_FOLDER_PATH)/Resources/vibetunnel",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/zsh;
			shellScript = "# Build web frontend conditionally based on hash\necho \"Checking if web frontend needs rebuild...\"\n\n# Run the conditional build script\n\"${SRCROOT}/scripts/build-web-frontend.sh\"\n\nif [ $? -ne 0 ]; then\n    echo \"error: Web frontend build failed\"\n    exit 1\nfi\n";
		};
		C3D4E5F6A7B8C9D0E1F23456 /* Install Build Dependencies */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Install Build Dependencies";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/zsh;
			shellScript = "# Check for Node.js availability\necho \"Checking build dependencies...\"\n\n# Run the install script\n\"${SRCROOT}/scripts/install-node.sh\"\n\nif [ $? -ne 0 ]; then\n    echo \"error: Node.js is required to build VibeTunnel\"\n    exit 1\nfi\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		788687ED2DFF4FCB00B22C15 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		788687FA2DFF4FCB00B22C15 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		788688002DFF4FCB00B22C15 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 788687F02DFF4FCB00B22C15 /* VibeTunnel */;
			targetProxy = 788687FF2DFF4FCB00B22C15 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		788688102DFF4FCC00B22C15 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 788687F32DFF4FCB00B22C15 /* VibeTunnel */;
			baseConfigurationReferenceRelativePath = Shared.xcconfig;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_STRICT_CONCURRENCY = complete;
				SWIFT_VERSION = 6.0;
			};
			name = Debug;
		};
		788688112DFF4FCC00B22C15 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 788687F32DFF4FCB00B22C15 /* VibeTunnel */;
			baseConfigurationReferenceRelativePath = Shared.xcconfig;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_STRICT_CONCURRENCY = complete;
				SWIFT_VERSION = 6.0;
			};
			name = Release;
		};
		788688132DFF4FCC00B22C15 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = VibeTunnel/VibeTunnel.entitlements;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = "$(inherited)";
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "";
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = VibeTunnel/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = VibeTunnel;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.developer-tools";
				INFOPLIST_KEY_LSUIElement = YES;
				INFOPLIST_KEY_NSAppleEventsUsageDescription = "VibeTunnel uses AppleScript to spawn a terminal when you create a new session in the dashboard. This allows VibeTunnel to automatically open your preferred terminal application and connect it to the remote session.";
				INFOPLIST_KEY_NSHumanReadableCopyright = "Copyright © 2025 VibeTunnel Team. All rights reserved.";
				INFOPLIST_KEY_NSMainStoryboardFile = Main;
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = sh.vibetunnel.vibetunnel.debug;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				VIBETUNNEL_USE_CUSTOM_NODE = YES;
			};
			name = Debug;
		};
		788688142DFF4FCC00B22C15 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = VibeTunnel/VibeTunnel.entitlements;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = "$(inherited)";
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "";
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = VibeTunnel/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = VibeTunnel;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.developer-tools";
				INFOPLIST_KEY_LSUIElement = YES;
				INFOPLIST_KEY_NSAppleEventsUsageDescription = "VibeTunnel uses AppleScript to spawn a terminal when you create a new session in the dashboard. This allows VibeTunnel to automatically open your preferred terminal application and connect it to the remote session.";
				INFOPLIST_KEY_NSHumanReadableCopyright = "Copyright © 2025 VibeTunnel Team. All rights reserved.";
				INFOPLIST_KEY_NSMainStoryboardFile = Main;
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = sh.vibetunnel.vibetunnel;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				VIBETUNNEL_USE_CUSTOM_NODE = YES;
			};
			name = Release;
		};
		788688162DFF4FCC00B22C15 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(inherited)";
				DEAD_CODE_STRIPPING = YES;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = sh.vibetunnel.vibetunnelTests.debug;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/VibeTunnel.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/VibeTunnel";
			};
			name = Debug;
		};
		788688172DFF4FCC00B22C15 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(inherited)";
				DEAD_CODE_STRIPPING = YES;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = sh.vibetunnel.vibetunnelTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/VibeTunnel.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/VibeTunnel";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		788687EC2DFF4FCB00B22C15 /* Build configuration list for PBXProject "VibeTunnel-Mac" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				788688102DFF4FCC00B22C15 /* Debug */,
				788688112DFF4FCC00B22C15 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		788688152DFF4FCC00B22C15 /* Build configuration list for PBXNativeTarget "VibeTunnel" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				788688132DFF4FCC00B22C15 /* Debug */,
				788688142DFF4FCC00B22C15 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		788688182DFF4FCC00B22C15 /* Build configuration list for PBXNativeTarget "VibeTunnelTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				788688162DFF4FCC00B22C15 /* Debug */,
				788688172DFF4FCC00B22C15 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		78AD8B8E2E051EB50009725C /* XCRemoteSwiftPackageReference "swift-log" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/apple/swift-log.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.6.3;
			};
		};
		89D01D842CB5D7DC0075D8BD /* XCRemoteSwiftPackageReference "Sparkle" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/sparkle-project/Sparkle";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.7.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		78AD8B942E051ED40009725C /* Logging */ = {
			isa = XCSwiftPackageProductDependency;
			package = 78AD8B8E2E051EB50009725C /* XCRemoteSwiftPackageReference "swift-log" */;
			productName = Logging;
		};
		89D01D852CB5D7DC0075D8BD /* Sparkle */ = {
			isa = XCSwiftPackageProductDependency;
			package = 89D01D842CB5D7DC0075D8BD /* XCRemoteSwiftPackageReference "Sparkle" */;
			productName = Sparkle;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 788687E92DFF4FCB00B22C15 /* Project object */;
}
