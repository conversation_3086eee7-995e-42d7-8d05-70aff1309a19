// Shared Configuration
// This file contains settings shared across all configurations

// Include version configuration
#include "version.xcconfig"

// Include local development settings (if exists)
// This file is ignored by git and contains personal development team settings
#include? "Local.xcconfig"

// Default values (can be overridden in Local.xcconfig)
// These will be used if Local.xcconfig doesn't exist or doesn't define them
DEVELOPMENT_TEAM = $(inherited)
CODE_SIGN_STYLE = $(inherited)

// Swift version and concurrency settings
SWIFT_VERSION = 6.0
SWIFT_STRICT_CONCURRENCY = complete
