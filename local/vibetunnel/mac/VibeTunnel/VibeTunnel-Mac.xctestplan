{"configurations": [{"id": "9D7E1994-9FC3-4F5E-B761-5D212EC7615B", "name": "Test Scheme Action", "options": {}}], "defaultOptions": {"codeCoverage": true, "performanceAntipatternCheckerEnabled": true, "targetForVariableExpansion": {"containerPath": "container:VibeTunnel-Mac.xcodeproj", "identifier": "788687F02DFF4FCB00B22C15", "name": "VibeTunnel"}}, "testTargets": [{"target": {"containerPath": "container:VibeTunnel-Mac.xcodeproj", "identifier": "788687FD2DFF4FCB00B22C15", "name": "VibeTunnelTests"}}], "version": 1}