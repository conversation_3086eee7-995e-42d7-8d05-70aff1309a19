{"name": "vibetunnel-tauri", "version": "1.0.0", "description": "Tauri system tray app for VibeTunnel terminal multiplexer", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "typecheck": "tsc --noEmit", "lint": "eslint . --ext .ts,.js", "test": "web-test-runner", "test:watch": "web-test-runner --watch"}, "devDependencies": {"@tauri-apps/cli": "^2.0.0-rc.18", "@types/node": "^20.12.0", "@web/test-runner": "^0.18.0", "@web/test-runner-playwright": "^0.11.0", "@open-wc/testing": "^4.0.0", "@esm-bundle/chai": "^4.3.4-fix.0", "typescript": "^5.4.5", "vite": "^6.3.5"}, "keywords": ["terminal", "multiplexer", "tauri", "system-tray"], "author": "", "license": "MIT", "dependencies": {"@lit/context": "^1.1.3", "@lit/task": "^1.0.1", "lit": "^3.3.0"}}