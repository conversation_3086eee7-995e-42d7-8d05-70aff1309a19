# Tauri build artifacts
/target/
target/

# WiX output for Windows installer
WixTools/

# Generated binary outputs
*.exe
*.dll
*.dylib
*.so

# macOS specific
.DS_Store
*.app
*.dmg

# Windows specific
Thumbs.db
ehthumbs.db

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
*~
.project
.classpath
.settings/

# Rust specific
Cargo.lock
**/*.rs.bk
*.pdb

# Debug artifacts
*.dSYM/

# Test results
tarpaulin-report.html
cobertura.xml

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Log files
*.log
logs/

# Environment files
.env
.env.local
.env.*.local

# Backup files
*.bak
*.backup

# Coverage reports
coverage/
*.lcov

# Profiling data
*.profdata
*.profraw

# Tauri generated schemas (optional - uncomment if you want to regenerate these)
# /gen/

# Bundle outputs
/bundle/
dist/
dist-ssr/

# Frontend build outputs
../public/
../public/assets/

# Node modules (if using Node.js build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Package manager files
pnpm-lock.yaml
yarn.lock
package-lock.json

# Custom build artifacts
/out/
/build/
/release/

# Crashdumps
*.stackdump
core

# Security and secrets
*.key
*.pem
*.p12
*.pfx
secrets/