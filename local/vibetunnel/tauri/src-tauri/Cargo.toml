[package]
name = "vibetunnel"
version = "0.1.0"
description = "VibeTunnel - Cross-platform terminal session manager"
authors = ["VibeTunnel Team"]
edition = "2021"

[package.metadata.bundle]
identifier = "com.vibetunnel.app"
copyright = "Copyright © 2024 VibeTunnel Team"
category = "DeveloperTool"
short_description = "Terminal session manager with remote access"
long_description = "VibeTunnel is a powerful terminal session manager that allows you to create, manage, and share terminal sessions. Features include multiple concurrent sessions, remote access capabilities, and a modern web-based interface."

[lib]
name = "tauri_lib"
crate-type = ["lib", "cdylib", "staticlib"]

[build-dependencies]
tauri-build = { version = "2.2.0", features = [] }

[dependencies]
tauri = { version = "2.5.1", features = ["unstable", "devtools", "image-png", "image-ico", "tray-icon"] }
tauri-plugin-shell = "2.2.2"
tauri-plugin-dialog = "2.2.2"
tauri-plugin-process = "2.2.2"
tauri-plugin-fs = "2.3.0"
tauri-plugin-http = "2.4.4"
tauri-plugin-notification = "2.2.3"
tauri-plugin-updater = "2.8.1"
tauri-plugin-window-state = "2.2.3"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tokio = { version = "1", features = ["full"] }
uuid = { version = "1", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

# Terminal handling
portable-pty = "0.9"
bytes = "1"
futures = "0.3"

# WebSocket server
tokio-tungstenite = "0.27"
tungstenite = "0.27"

# SSE streaming
async-stream = "0.3"
tokio-stream = "0.1"

# HTTP server
axum = { version = "0.8", features = ["ws"] }
tower = "0.5"
tower-http = { version = "0.6", features = ["fs", "cors"] }

# Settings and storage
directories = "6"
toml = "0.8"

# Utilities
open = "5"

# File system
dirs = "6"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Auto-launch
auto-launch = "0.5"

# System info
whoami = "1"
hostname = "0.4"

# ngrok integration and API client
which = "8"
reqwest = { version = "0.12", features = ["json", "blocking"] }

# Authentication
base64 = "0.22"
sha2 = "0.10"

# Keychain/Credential Storage
keyring = "3"

# Debug features
num_cpus = "1"

# Network utilities
[target.'cfg(unix)'.dependencies]
nix = { version = "0.30", features = ["net", "signal", "process"] }

[target.'cfg(windows)'.dependencies]
ipconfig = "0.3"
windows = { version = "0.61", features = ["Win32_Foundation", "Win32_Security", "Win32_System_Threading", "Win32_UI_WindowsAndMessaging"] }

[target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
tauri-plugin-single-instance = "2.2.4"

[profile.release]
panic = "abort"
codegen-units = 1
lto = true
opt-level = "s"
strip = true

[dev-dependencies]
mockito = "1.7"
