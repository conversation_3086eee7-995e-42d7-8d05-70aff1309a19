{"all-windows": {"identifier": "all-windows", "description": "Capability for all application windows", "local": true, "windows": ["main", "settings", "welcome", "server-console", "api-testing"], "permissions": ["core:default", "core:window:default", "core:window:allow-create", "core:window:allow-close", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-start-dragging", "core:window:allow-set-title", "core:app:default", "core:path:default", "core:event:default", "core:webview:default", "shell:default", "shell:allow-open", "dialog:default", "fs:default", "fs:allow-read-file", "fs:allow-write-file", "fs:allow-read-dir", "fs:allow-copy-file", "fs:allow-mkdir", "fs:allow-remove", "fs:allow-rename", "fs:allow-exists", "fs:scope-appdata-recursive", "fs:scope-resource-recursive", "fs:scope-temp-recursive", "fs:allow-home-read-recursive", "fs:allow-home-write-recursive", "process:default", "process:allow-exit", "process:allow-restart", "http:default", "http:allow-fetch", "notification:default", "notification:allow-is-permission-granted", "notification:allow-request-permission", "notification:allow-notify", "window-state:allow-restore-state", "window-state:allow-save-window-state", "updater:default"]}, "default": {"identifier": "default", "description": "Default capability for the application", "local": true, "windows": ["main"], "permissions": ["core:default", "core:window:default", "core:window:allow-create", "core:window:allow-close", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-start-dragging", "core:window:allow-set-title", "core:app:default", "core:path:default", "core:event:default", "core:webview:default", "shell:default", "shell:allow-open", "dialog:default", "fs:default", "fs:allow-read-file", "fs:allow-write-file", "fs:allow-read-dir", "fs:allow-copy-file", "fs:allow-mkdir", "fs:allow-remove", "fs:allow-rename", "fs:allow-exists", "fs:scope-appdata-recursive", "fs:scope-resource-recursive", "fs:scope-temp-recursive", "fs:allow-home-read-recursive", "fs:allow-home-write-recursive", "process:default", "process:allow-exit", "process:allow-restart", "http:default", "http:allow-fetch", "notification:default", "notification:allow-is-permission-granted", "notification:allow-request-permission", "notification:allow-notify", "window-state:allow-restore-state", "window-state:allow-save-window-state", "updater:default"]}, "settings": {"identifier": "settings", "description": "Capability for the settings window", "local": true, "windows": ["settings"], "permissions": ["core:default", "core:window:default", "core:window:allow-close", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-start-dragging", "core:window:allow-set-title", "core:app:default", "core:path:default", "core:event:default", "core:webview:default", "shell:default", "shell:allow-open", "dialog:default", "fs:default", "fs:allow-read-file", "fs:allow-write-file", "fs:allow-read-dir", "fs:allow-copy-file", "fs:allow-mkdir", "fs:allow-remove", "fs:allow-rename", "fs:allow-exists", "fs:scope-appdata-recursive", "fs:scope-resource-recursive", "fs:scope-temp-recursive", "fs:allow-home-read-recursive", "fs:allow-home-write-recursive", "process:default", "process:allow-exit", "process:allow-restart", "http:default", "http:allow-fetch", "notification:default", "notification:allow-is-permission-granted", "notification:allow-request-permission", "notification:allow-notify", "window-state:allow-restore-state", "window-state:allow-save-window-state", "updater:default"]}}