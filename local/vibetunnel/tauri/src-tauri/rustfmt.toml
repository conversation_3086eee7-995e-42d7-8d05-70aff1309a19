# Rust formatting configuration
edition = "2021"
max_width = 100
tab_spaces = 4
use_small_heuristics = "Default"
reorder_imports = true
reorder_modules = true
remove_nested_parens = true
match_arm_blocks = true
use_field_init_shorthand = true
use_try_shorthand = true
newline_style = "Auto"
format_code_in_doc_comments = true
wrap_comments = true
comment_width = 80
normalize_comments = true
normalize_doc_attributes = true
format_strings = true
format_macro_matchers = true
format_macro_bodies = true
imports_granularity = "Crate"
group_imports = "StdExternalCrate"