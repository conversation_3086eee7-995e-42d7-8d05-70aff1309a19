{"$schema": "https://schema.tauri.app/config/2.0.0", "productName": "VibeTunnel", "identifier": "com.vibetunnel.app", "build": {"beforeDevCommand": "cd ../web && npm run build", "beforeBuildCommand": "cd ../web && npm run build", "frontendDist": "../public"}, "app": {"windows": [], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["icons/menu-bar-icon.png", "icons/<EMAIL>", "icons/tray-icon.png", "icons/<EMAIL>", "../../web/native/vibetunnel", "../../web/native/*.node", "../../web/native/spawn-helper", "../../web/public/**/*"], "macOS": {"frameworks": [], "minimumSystemVersion": "10.15", "exceptionDomain": "localhost", "signingIdentity": null, "providerShortName": null, "entitlements": "entitlements.plist"}, "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}, "linux": {"deb": {"depends": []}, "appimage": {"bundleMediaFramework": true}}}}