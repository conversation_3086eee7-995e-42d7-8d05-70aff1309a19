{"compilerOptions": {"target": "ES2022", "module": "ESNext", "lib": ["ES2022", "DOM", "DOM.Iterable"], "experimentalDecorators": true, "useDefineForClassFields": false, "moduleResolution": "bundler", "allowJs": true, "checkJs": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "skipLibCheck": true, "paths": {"@components/*": ["./src/components/*"], "@shared/*": ["./src/components/shared/*"], "@contexts/*": ["./src/contexts/*"], "@utils/*": ["./src/utils/*"]}}, "include": ["src/**/*.ts", "src/**/*.js", "vite.config.js"], "exclude": ["node_modules", "dist", "public"]}