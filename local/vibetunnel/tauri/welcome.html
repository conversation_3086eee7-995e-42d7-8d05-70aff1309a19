<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/icon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Welcome to VibeTunnel</title>
    <style>
      html, body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        background: transparent;
      }
      
      /* Make body transparent for window transparency */
      body {
        background: transparent !important;
      }
      
      /* Theme detection */
      @media (prefers-color-scheme: dark) {
        html { color-scheme: dark; }
      }
      
      @media (prefers-color-scheme: light) {
        html { color-scheme: light; }
        html.light { color-scheme: light; }
      }
    </style>
  </head>
  <body>
    <welcome-app></welcome-app>
    <script type="module">
      import './src/components/welcome-app.ts';
      
      // Apply theme class to html element
      const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      document.documentElement.classList.toggle('light', !isDark);
      
      // Listen for theme changes
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        document.documentElement.classList.toggle('light', !e.matches);
      });
    </script>
  </body>
</html>